---
id: installation-guide
title: Installation Guide
sidebar_label: Installation Guide
sidebar_position: 4
---

# Getting Started with Cloc SDK

Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.

## Prerequisites

- An Ever Cloc account, which you can create [here](https://app.cloc.ai)
- Basic familiarity with React and TypeScript
- Understanding of React Context and Providers

## Environment Setup

### Configure Environment Variables

Create a `.env.local` file in your project root:

```
# Cloc API URL
CLOC_API_URL=https://api.cloc.ai/api
```

### Install SDK Packages

Install the core Cloc SDK packages and their peer dependencies:

<Tabs>

<TabItem value="npm" label="npm" >
```bash
# Core SDK packages
npm install @cloc/atoms
```
</TabItem>

<TabItem value="pnpm" label="pnpm" >
```bash
# Core SDK packages
pnpm add @cloc/atoms
```
</TabItem>

<TabItem value="yarn" label="yarn" >
```bash
# Core SDK packages
yarn add  @cloc/atoms
```
</TabItem>

</Tabs>

Above installation will auto-install all required Cloc packages and peer dependencies.

### CSS Imports

Import the required stylesheets in your application either in your main root component or global CSS file:

<Tabs>
  <TabItem value="js" label="JS/TS Import" default>
  
  ```tsx
  // In your main root component (e.g. App.tsx)
  import "@cloc/atoms/styles.css";
  import "@cloc/ui/styles.css";
  ```
  
  </TabItem>
  <TabItem value="css" label="CSS Import">
  
  ```css
  /* In your main CSS file (e.g. global.css) */
  @import "@cloc/atoms/styles.css";
  @import "@cloc/ui/styles.css";
  ```
  
  </TabItem>
</Tabs>

:::info Stylesheet Order
Import @cloc/atoms and @cloc/ui styles before your custom styles to ensure proper CSS cascade and customization capabilities.
:::

## Framework-Specific Setup

<Tabs>
<TabItem value="next-app-router" label="Next.js App Router" default>

#### 1. Create Root Layout with Providers

Create or update your root layout (`src/app/layout.tsx`):

```tsx
import { ClocProvider } from "@cloc/atoms";
import "./globals.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
          }}
        >
          {children}
        </ClocProvider>
      </body>
    </html>
  );
}
```

#### 2. Tailwind CSS Configuration

Update your `tailwind.config.js` to include Cloc components:

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  transpilePackages: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
```

</TabItem>
<TabItem value="next-pages-router" label="Next.js Pages Router">

#### 1. Update \_app.tsx

```tsx
import type { AppProps } from "next/app";
import { ClocProvider } from "@cloc/atoms";
import "../styles/globals.css";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ClocProvider
      config={{
        apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
      }}
    >
      <Component {...pageProps} />
    </ClocProvider>
  );
}
```

</TabItem>
<TabItem value="remix" label="Remix">

#### 1. Update root.tsx

```tsx
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import { ClocProvider } from "@cloc/atoms";

export default function App() {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.CLOC_API_URL,
          }}
        >
          <Outlet />
        </ClocProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
```

#### 2. Update remix.config.js

```js
/** @type {import('@remix-run/dev').AppConfig} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/atoms/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/ui/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "var(--cloc-primary)",
        secondary: "var(--cloc-secondary)",
        accent: "var(--cloc-accent)",
        background: "var(--cloc-background)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
```

</TabItem>
<TabItem value="vite" label="Vite">

#### 1. Update main.tsx

```tsx
import React from "react";
import ReactDOM from "react-dom/client";
import { ClocProvider } from "@cloc/atoms";
import App from "./App";
import "./index.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ClocProvider
      config={{
        apiUrl: import.meta.env.VITE_CLOC_API_URL,
      }}
    >
      <App />
    </ClocProvider>
  </React.StrictMode>
);
```

#### 2. Update vite.config.ts

```ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
  },
  define: {
    global: "globalThis",
  },
});
```

</TabItem>
</Tabs>

## Quick Start Example

Create a test component to verify your installation:

import ComponentPreview from "@site/src/components/ui/component-preview";
import Preview from "@site/src/components/preview/preview";
import TabItem from "@theme/TabItem";
import Tabs from "@theme/Tabs";

### Basic Timer Component

<ComponentPreview code={`
// src/app/page.tsx (Next.js App Router)
// or src/pages/index.tsx (Next.js Pages Router)
// or src/App.tsx (Vite/CRA)

import { ModernCloc, ClocThemeToggle } from "@cloc/atoms";

export default function HomePage() {
    return (
        <div className="flex min-h-screen flex-col items-center justify-center p-4 space-y-8">
            <div className="text-center space-y-4">
                <h1 className="text-4xl font-bold text-foreground">Welcome to Cloc SDK</h1>
                <p className="text-muted-foreground max-w-md">Your time tracking components are ready to use!</p>
            </div>
            <div className="flex flex-col items-center space-y-4">
                <ModernCloc
                    expanded={false}
                    showProgress={true}
                    variant="default"
                    size="default"
                    separator=":"
                />
                <ClocThemeToggle />
            </div>
        </div>
    );
}
`
} language={"tsx"} >
<Preview />
</ComponentPreview>

### Advanced Example with Multiple Components

<ComponentPreview code={
`import {ModernCloc, ClocProvider, ClocLoginDialog, ClocThemeToggle, DailyActivityDisplayer, useClocContext} from "@cloc/atoms";

import { Button } from "@cloc/ui";

function TimerDashboard() {
    const { authenticatedUser } = useClocContext();

    return (
      <div className="container mx-auto p-6 space-y-8">
        <header className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Time Tracking Dashboard</h1>
          <div className="flex items-center space-x-4">
            <ClocThemeToggle />
            {!authenticatedUser && <ClocLoginDialog />}
          </div>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="col-span-1 md:col-span-2 lg:col-span-1">
            <ModernCloc expanded={true} showProgress={true} />
          </div>

          <div className="col-span-1">
            <DailyActivityDisplayer />
          </div>

          <div className="col-span-1">
            {/* Add more components as needed */}
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Quick Actions</h3>
              <div className="space-y-2">
                <Button className="w-full">Start New Timer</Button>
                <Button className="w-full">View Reports</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
}

export default function App() {
    return <TimerDashboard />;
}
`
} language={"tsx"} >
<Preview />
</ComponentPreview>

## Installation Verification

Follow these steps to verify your installation is working correctly:

### 1. Check Package Installation

Verify all packages are installed correctly:

```bash
# Check if @cloc/atoms is installed
npm list @cloc/atoms

# Check peer dependencies
npm list react react-dom @emotion/react theme-ui next-themes

# Check for any missing peer dependencies
npm ls --depth=0
```

### 2. Build Test

Ensure your application builds without errors:

```bash
npm run build
```

### 3. Runtime Verification

Start your development server and check for:

```bash
# Start development server
npm run dev
```

**Expected Results:**

- ✅ No console errors related to @cloc/atoms
- ✅ Timer component renders correctly (if implemented)
- ✅ Theme toggle works (if implemented)
- ✅ No missing dependency warnings

## Troubleshooting Guide

### Common Issues & Solutions

1. **Component Styling Issues**

   ```
   Warning: Prop `className` did not match

   ```

   - Add required CSS imports
   - Check Tailwind configuration
   - Verify theme provider setup

2. **Timer Synchronization Issues (if implemented)**

   ```shell
   Error: Failed to sync timer state

   ```

   - Check network connectivity
   - Verify API endpoint configuration
   - Ensure User has permissions to track time

<details>
<summary><strong>Module Resolution Errors</strong></summary>

**Problem:**

```bash
Module not found: Can't resolve '@cloc/atoms/styles.css'
```

**Solutions:**

1. Verify package installation:

   ```bash
   npm list @cloc/atoms
   ```

2. Clear node_modules and reinstall:

   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. Check import path:

   ```tsx
   // Correct
   import "@cloc/atoms/styles.css";

   // Incorrect
   import "@cloc/atoms/dist/styles.css";
   ```

</details>

### Build Errors

<details>
<summary><strong>TypeScript Compilation Errors</strong></summary>

**Problem:**

```bash
Type error: Cannot find module '@cloc/atoms' or its corresponding type declarations
```

**Solutions:**

1. Ensure TypeScript version compatibility:

   ```bash
   npm install typescript@^5.0.0
   ```

2. Add to tsconfig.json:

   ```json
   {
     "compilerOptions": {
       "moduleResolution": "node",
       "esModuleInterop": true,
       "allowSyntheticDefaultImports": true,
       "skipLibCheck": true
     }
   }
   ```

3. Clear TypeScript cache:
   ```bash
   npx tsc --build --clean
   ```

</details>

<details>
<summary><strong>Bundler Configuration Issues</strong></summary>

**Problem:**

```bash
Error: Failed to resolve import "@cloc/atoms" from "src/App.tsx"
```

**Solutions:**

For **Vite**:

```ts
// vite.config.ts
export default defineConfig({
  optimizeDeps: {
    include: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
  },
});
```

For **Webpack** (CRA):

```js
// craco.config.js
module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        stream: require.resolve("stream-browserify"),
        buffer: require.resolve("buffer"),
      };
      return webpackConfig;
    },
  },
};
```

For **Next.js**:

```js
// next.config.js
module.exports = {
  transpilePackages: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
};
```

</details>

### Runtime Errors

<details>
<summary><strong>Provider Configuration Errors</strong></summary>

**Problem:**

```bash
Error: useClocContext must be used within a ClocProvider
```

**Solutions:**

1. Ensure ClocProvider wraps your app:

   ```tsx
   // ✅ Correct
   function App() {
     return (
       <ClocProvider
         config={
           {
             /* ... */
           }
         }
       >
         <YourComponents />
       </ClocProvider>
     );
   }

   // ❌ Incorrect
   function App() {
     return (
       <div>
         <ClocProvider
           config={
             {
               /* ... */
             }
           }
         />
         <YourComponents />
       </div>
     );
   }
   ```

2. Check provider configuration:
   ```tsx
   <ClocProvider
     config={{
       apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
     }}
   >
   ```

</details>

<details>
<summary><strong>Styling and Theme Issues</strong></summary>

**Problem:**

```bash
Warning: Prop `className` did not match. Server: "..." Client: "..."
```

**Solutions:**

1. Ensure CSS imports order:

   ```tsx
   // 1. Cloc styles first
   import "@cloc/atoms/styles.css";
   // 2. Framework styles
   import "tailwindcss/tailwind.css";
   // 3. Custom styles last
   import "./globals.css";
   ```

2. Add suppressHydrationWarning for theme providers:

   ```tsx
   <html lang="en" suppressHydrationWarning>
   ```

</details>

### Performance Issues

<details>
<summary><strong>Bundle Size Optimization</strong></summary>

**Problem:** Large bundle size affecting performance

**Solutions:**

1. Use tree shaking with named imports:

   ```tsx
   // ✅ Good - tree shakeable
   import { ModernCloc, ClocThemeToggle } from "@cloc/atoms";

   // ❌ Bad - imports everything
   import * as ClocAtoms from "@cloc/atoms";
   ```

2. Lazy load components:

   ```tsx
   import { lazy, Suspense } from "react";

   const ModernCloc = lazy(() =>
     import("@cloc/atoms").then((module) => ({ default: module.ModernCloc }))
   );

   function App() {
     return (
       <Suspense fallback={<div>Loading...</div>}>
         <ModernCloc />
       </Suspense>
     );
   }
   ```

3. Configure bundle analyzer:
   ```bash
   npm install --save-dev @next/bundle-analyzer
   ```

</details>

### Framework-Specific Issues

<details>
<summary><strong>Next.js Specific Issues</strong></summary>

**Problem:** SSR/Hydration mismatches

**Solutions:**

1. Use dynamic imports for client-only components:

   ```tsx
   import dynamic from "next/dynamic";

   const ModernCloc = dynamic(
     () => import("@cloc/atoms").then((mod) => mod.ModernCloc),
     { ssr: false }
   );
   ```

2. Add transpilePackages to next.config.js:
   ```js
   module.exports = {
     transpilePackages: ["@cloc/atoms", "@cloc/ui"],
   };
   ```

</details>

<details>
<summary><strong>Remix Specific Issues</strong></summary>

**Problem:** Server-side rendering errors

**Solutions:**


1. Configure serverDependenciesToBundle:
   ```js
   // remix.config.js
   module.exports = {
     serverDependenciesToBundle: ["@cloc/atoms"],
   };
   ```

</details>

:::warning Getting Stuck?
If you're still experiencing issues after trying these solutions, please check our [GitHub Issues](https://github.com/ever-co/ever-cloc/issues) or reach out to our support team.
:::

## Advanced Configuration

### Custom Theming

<details>
<summary><strong>Advanced Theme Customization</strong></summary>

```tsx
// theme.config.ts
export const customTheme = {
  colors: {
    primary: "#6366f1",
    secondary: "#8b5cf6",
    accent: "#f59e0b",
    background: "#ffffff",
    foreground: "#1f2937",
    muted: "#f3f4f6",
    "muted-foreground": "#6b7280",
    border: "#e5e7eb",
    input: "#ffffff",
    ring: "#6366f1",
  },
  borderRadius: {
    lg: "0.5rem",
    md: "0.375rem",
    sm: "0.25rem",
  },
  fonts: {
    sans: ["Inter", "system-ui", "sans-serif"],
    mono: ["JetBrains Mono", "monospace"],
  },
};

// App.tsx
<ClocProvider
  config={
    {
      /* ... */
    }
  }
  theme={customTheme}
>
  {children}
</ClocProvider>;
```

</details>

## Testing Setup

### Unit Testing with Jest

<details>
<summary><strong>Jest Configuration</strong></summary>

```js
// jest.config.js
module.exports = {
  testEnvironment: "jsdom",
  setupFilesAfterEnv: ["<rootDir>/src/setupTests.ts"],
  moduleNameMapping: {
    "^@cloc/atoms$": "<rootDir>/node_modules/@cloc/atoms/dist/index.es.js",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
  },
  transformIgnorePatterns: [
    "node_modules/(?!(@cloc/atoms|@cloc/ui|@cloc/api|@cloc/types)/)",
  ],
};
```

```tsx
// src/setupTests.ts
import "@testing-library/jest-dom";

// Mock environment variables
process.env.NEXT_PUBLIC_CLOC_API_URL = "https://api.cloc.ai/api";
```

</details>

### Component Testing Example

```tsx
// __tests__/ModernCloc.test.tsx
import { render, screen } from "@testing-library/react";
import { ClocProvider, ModernCloc } from "@cloc/atoms";

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ClocProvider
    config={{
      apiUrl: "https://api.cloc.ai/api",
    }}
  >
    {children}
  </ClocProvider>
);

describe("ModernCloc", () => {
  it("renders timer component", () => {
    render(
      <TestWrapper>
        <ModernCloc variant="default" size="default" />
      </TestWrapper>
    );

    expect(screen.getByRole("timer")).toBeInTheDocument();
  });

  it("displays correct time format", () => {
    render(
      <TestWrapper>
        <ModernCloc variant="default" separator=":" />
      </TestWrapper>
    );

    expect(screen.getByText(/\d{2}:\d{2}:\d{2}/)).toBeInTheDocument();
  });
});
```

## Getting Help

Need additional support? We're here to help:

- 📚 [Documentation](https://docs.cloc.ai/)
- 📧 [Email Support](mailto:<EMAIL>)
- 🐛 [GitHub Issues](https://github.com/ever-co/ever-cloc/issues)
- 📖 [API Reference](/docs/api)

## Next Steps

Congratulations! You've successfully installed and configured Cloc SDK. Here's what to explore next:

:::info Explore the SDK

- **[Component Library](/docs/components)** - Browse all available React components
- **[API Reference](/docs/api)** - Complete API documentation and examples
- **[Tracking Guide](/docs/core-libraries/cloc-tracking)** - Implement advanced analytics and tracking

:::

### Quick Links

- 🎯 [Timer Components Guide](/docs/components/timer) - Learn about all timer variants
- 🎨 [Theming Guide](/docs/theming) - Customize the look and feel

Start building amazing time-tracking experiences with @cloc/atoms! 🚀

---

**Happy coding!** If you build something awesome with @cloc/atoms, we'd love to hear about it. Share your projects with the community on our Discord server.
