import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/__docusaurus/debug',
    component: ComponentCreator('/__docusaurus/debug', '5ff'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/config',
    component: ComponentCreator('/__docusaurus/debug/config', '5ba'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/content',
    component: ComponentCreator('/__docusaurus/debug/content', 'a2b'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/globalData',
    component: ComponentCreator('/__docusaurus/debug/globalData', 'c3c'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/metadata',
    component: ComponentCreator('/__docusaurus/debug/metadata', '156'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/registry',
    component: ComponentCreator('/__docusaurus/debug/registry', '88c'),
    exact: true
  },
  {
    path: '/__docusaurus/debug/routes',
    component: ComponentCreator('/__docusaurus/debug/routes', '000'),
    exact: true
  },
  {
    path: '/cloc-test',
    component: ComponentCreator('/cloc-test', '8ab'),
    exact: true
  },
  {
    path: '/help',
    component: ComponentCreator('/help', '072'),
    exact: true
  },
  {
    path: '/markdown-page',
    component: ComponentCreator('/markdown-page', '3d7'),
    exact: true
  },
  {
    path: '/search',
    component: ComponentCreator('/search', '822'),
    exact: true
  },
  {
    path: '/users',
    component: ComponentCreator('/users', '677'),
    exact: true
  },
  {
    path: '/docs',
    component: ComponentCreator('/docs', 'b57'),
    routes: [
      {
        path: '/docs',
        component: ComponentCreator('/docs', 'df5'),
        routes: [
          {
            path: '/docs',
            component: ComponentCreator('/docs', '429'),
            routes: [
              {
                path: '/docs/api/',
                component: ComponentCreator('/docs/api/', '5e5'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/api/api-docs',
                component: ComponentCreator('/docs/api/api-docs', 'cab'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/api/api-reference',
                component: ComponentCreator('/docs/api/api-reference', '872'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/cloc-sdk',
                component: ComponentCreator('/docs/cloc-sdk', '0bf'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/components/',
                component: ComponentCreator('/docs/components/', '4e7'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/components/reports',
                component: ComponentCreator('/docs/components/reports', 'f16'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/components/timer',
                component: ComponentCreator('/docs/components/timer', '45c'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/components/ui',
                component: ComponentCreator('/docs/components/ui', 'f7d'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/',
                component: ComponentCreator('/docs/core-libraries/', 'cc6'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/cloc-api',
                component: ComponentCreator('/docs/core-libraries/cloc-api', 'c82'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/cloc-atoms',
                component: ComponentCreator('/docs/core-libraries/cloc-atoms', 'af3'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/cloc-tracking',
                component: ComponentCreator('/docs/core-libraries/cloc-tracking', '4ac'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/cloc-types',
                component: ComponentCreator('/docs/core-libraries/cloc-types', '119'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/core-libraries/cloc-ui',
                component: ComponentCreator('/docs/core-libraries/cloc-ui', '791'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/examples-tutorials',
                component: ComponentCreator('/docs/examples-tutorials', '9b4'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/index',
                component: ComponentCreator('/docs/index', 'fed'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/introduction/',
                component: ComponentCreator('/docs/introduction/', 'aeb'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/introduction/installation-guide',
                component: ComponentCreator('/docs/introduction/installation-guide', '112'),
                exact: true,
                sidebar: "tutorialSidebar"
              },
              {
                path: '/docs/introduction/quick-start-example',
                component: ComponentCreator('/docs/introduction/quick-start-example', 'ce8'),
                exact: true,
                sidebar: "tutorialSidebar"
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/',
    component: ComponentCreator('/', 'e5f'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
